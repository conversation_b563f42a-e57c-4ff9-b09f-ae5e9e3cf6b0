#!/usr/bin/env python3
"""
Migration script to update connected account fields in chatbots table:
- Add connected_account_display_name (VARCHAR)
- Add connected_account_entity_type (VARCHAR)
- Change connected_account_id from VARCHAR to INTEGER
- Migrate existing data if any

Run this script to update the connected account structure in the chatbots table.
"""

import os
import sys
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import logging
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_database_url():
    """Get database URL from environment variables"""
    DB_USER = os.getenv("POSTGRES_USER", "sdwhatsapp")
    DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "sdwhatsapp")
    DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
    DB_PORT = "5432"
    DB_NAME = os.getenv("POSTGRES_DB", "sdwhatsapp")
    return f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def run_migration():
    """
    Update connected account fields in the chatbots table
    """
    try:
        # Get database URL
        database_url = get_database_url()
        logger.info(f"Connecting to database...")
        
        # Create engine
        engine = create_engine(database_url)
        
        # SQL statements to update connected account fields
        migration_sql = [
            # Add new connected account fields
            """
            ALTER TABLE chatbots 
            ADD COLUMN IF NOT EXISTS connected_account_display_name VARCHAR;
            """,
            """
            ALTER TABLE chatbots 
            ADD COLUMN IF NOT EXISTS connected_account_entity_type VARCHAR;
            """,
            # Create a temporary column for the new integer account ID
            """
            ALTER TABLE chatbots 
            ADD COLUMN IF NOT EXISTS connected_account_id_new INTEGER;
            """,
            # Try to migrate existing data (convert string IDs to integers if possible)
            """
            UPDATE chatbots 
            SET connected_account_id_new = CASE 
                WHEN connected_account_id ~ '^[0-9]+$' THEN connected_account_id::INTEGER
                ELSE NULL
            END
            WHERE connected_account_id IS NOT NULL;
            """,
            # Drop the old connected_account_id column
            """
            ALTER TABLE chatbots 
            DROP COLUMN IF EXISTS connected_account_id;
            """,
            # Rename the new column to the correct name
            """
            ALTER TABLE chatbots 
            RENAME COLUMN connected_account_id_new TO connected_account_id;
            """
        ]
        
        # Execute migration
        with engine.connect() as connection:
            # Start transaction
            trans = connection.begin()
            
            try:
                logger.info("Starting migration: Updating connected account fields...")
                
                for i, sql in enumerate(migration_sql, 1):
                    logger.info(f"Executing migration step {i}/{len(migration_sql)}...")
                    connection.execute(text(sql))
                    logger.info(f"✓ Migration step {i} completed")
                
                # Commit transaction
                trans.commit()
                logger.info("✓ Migration completed successfully!")
                
                # Verify the columns were updated
                logger.info("Verifying new columns...")
                result = connection.execute(text("""
                    SELECT column_name, data_type, is_nullable 
                    FROM information_schema.columns 
                    WHERE table_name = 'chatbots' 
                    AND column_name IN ('connected_account_display_name', 'connected_account_entity_type', 'connected_account_id')
                    ORDER BY column_name;
                """))
                
                columns = result.fetchall()
                logger.info("Current connected account columns:")
                for column in columns:
                    logger.info(f"  - {column[0]}: {column[1]} (nullable: {column[2]})")
                
                if len(columns) == 3:
                    logger.info("✓ All connected account columns are present")
                else:
                    logger.warning(f"⚠ Expected 3 columns, found {len(columns)}")
                
            except Exception as e:
                # Rollback transaction on error
                trans.rollback()
                logger.error(f"Migration failed: {str(e)}")
                raise
                
    except SQLAlchemyError as e:
        logger.error(f"Database error during migration: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during migration: {str(e)}")
        raise


def check_migration_needed():
    """
    Check if the migration is needed by verifying current table structure
    """
    try:
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        with engine.connect() as connection:
            # Check if new columns exist
            result = connection.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'chatbots' 
                AND column_name IN ('connected_account_display_name', 'connected_account_entity_type')
            """))
            
            existing_columns = [row[0] for row in result.fetchall()]
            
            # Check if connected_account_id is INTEGER
            result = connection.execute(text("""
                SELECT data_type 
                FROM information_schema.columns 
                WHERE table_name = 'chatbots' 
                AND column_name = 'connected_account_id'
            """))
            
            account_id_type = result.fetchone()
            
            if len(existing_columns) < 2 or (account_id_type and account_id_type[0] != 'integer'):
                logger.info("Migration is needed")
                return True
            else:
                logger.info("Migration not needed - columns already exist with correct types")
                return False
                
    except Exception as e:
        logger.error(f"Error checking migration status: {str(e)}")
        return True  # Assume migration is needed if we can't check


def rollback_migration():
    """
    Rollback the migration by reverting to the original structure
    """
    try:
        database_url = get_database_url()
        engine = create_engine(database_url)
        
        rollback_sql = [
            # Add back the original connected_account_id as VARCHAR
            "ALTER TABLE chatbots ADD COLUMN IF NOT EXISTS connected_account_id_old VARCHAR;",
            # Copy data back (convert integers to strings)
            "UPDATE chatbots SET connected_account_id_old = connected_account_id::VARCHAR WHERE connected_account_id IS NOT NULL;",
            # Drop new columns
            "ALTER TABLE chatbots DROP COLUMN IF EXISTS connected_account_display_name;",
            "ALTER TABLE chatbots DROP COLUMN IF EXISTS connected_account_entity_type;",
            "ALTER TABLE chatbots DROP COLUMN IF EXISTS connected_account_id;",
            # Rename old column back
            "ALTER TABLE chatbots RENAME COLUMN connected_account_id_old TO connected_account_id;"
        ]
        
        with engine.connect() as connection:
            trans = connection.begin()
            try:
                logger.info("Starting rollback...")
                for i, sql in enumerate(rollback_sql, 1):
                    logger.info(f"Executing rollback step {i}/{len(rollback_sql)}...")
                    connection.execute(text(sql))
                
                trans.commit()
                logger.info("✓ Rollback completed successfully!")
                
            except Exception as e:
                trans.rollback()
                logger.error(f"Rollback failed: {str(e)}")
                raise
                
    except Exception as e:
        logger.error(f"Error during rollback: {str(e)}")
        raise


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Migrate connected account fields in chatbots table")
    parser.add_argument("--check", action="store_true", help="Check if migration is needed")
    parser.add_argument("--rollback", action="store_true", help="Rollback the migration")
    
    args = parser.parse_args()
    
    if args.check:
        needed = check_migration_needed()
        sys.exit(0 if not needed else 1)
    elif args.rollback:
        rollback_migration()
    else:
        if check_migration_needed():
            run_migration()
        else:
            logger.info("Migration not needed")
