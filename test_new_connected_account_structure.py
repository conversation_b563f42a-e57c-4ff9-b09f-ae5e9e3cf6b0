#!/usr/bin/env python3
"""
Test script to verify the new connected account structure:
- New camelCase field names (welcomeMessage, thankYouMessage)
- Nested connectedAccount object with displayName, entityType, accountId
- Database model changes
- Service layer changes

This script tests the models and service layer directly.
"""

import json
import sys
import os
from typing import Dict, Any

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_database_model():
    """Test that the database model includes the new connected account fields"""
    print("Testing database model...")
    
    try:
        from app.models import Chatbot
        
        # Check if the new fields exist in the model
        model_fields = [attr for attr in dir(Chatbot) if not attr.startswith('_')]
        
        required_fields = [
            'connected_account_display_name', 
            'connected_account_entity_type', 
            'connected_account_id'
        ]
        found_fields = []
        
        for field in required_fields:
            if field in model_fields:
                found_fields.append(field)
                print(f"  ✓ {field} field found in Chatbot model")
            else:
                print(f"  ✗ {field} field not found in Chatbot model")
        
        if len(found_fields) == len(required_fields):
            print("✓ Database model test passed")
            return True
        else:
            print(f"✗ Database model test failed - {len(required_fields) - len(found_fields)} fields missing")
            return False
            
    except Exception as e:
        print(f"✗ Database model test failed: {str(e)}")
        return False


def test_pydantic_models():
    """Test that the Pydantic models support the new structure"""
    print("\nTesting Pydantic models...")
    
    try:
        from app.models import ChatbotCreate, ChatbotUpdate, ConnectedAccount
        
        # Test ConnectedAccount model
        print("  Testing ConnectedAccount model...")
        connected_account_data = {
            "displayName": "Test Account",
            "entityType": "LEAD",
            "accountId": 12345
        }
        
        connected_account = ConnectedAccount(**connected_account_data)
        print(f"    ✓ ConnectedAccount created: {connected_account.displayName}")
        
        # Test ChatbotCreate with new structure
        print("  Testing ChatbotCreate model...")
        chatbot_create_data = {
            "name": "Test Chatbot",
            "type": "AI",
            "description": "Test description",
            "welcomeMessage": "Hello!",
            "thankYouMessage": "Goodbye!",
            "connectedAccount": connected_account
        }
        
        chatbot_create = ChatbotCreate(**chatbot_create_data)
        print(f"    ✓ ChatbotCreate created: {chatbot_create.name}")
        print(f"    ✓ welcomeMessage: {chatbot_create.welcomeMessage}")
        print(f"    ✓ connectedAccount.displayName: {chatbot_create.connectedAccount.displayName}")
        
        # Test ChatbotUpdate with new structure
        print("  Testing ChatbotUpdate model...")
        chatbot_update_data = {
            "name": "Updated Test Chatbot",
            "welcomeMessage": "Updated Hello!",
            "connectedAccount": connected_account
        }
        
        chatbot_update = ChatbotUpdate(**chatbot_update_data)
        print(f"    ✓ ChatbotUpdate created: {chatbot_update.name}")
        
        # Test without connected account
        print("  Testing models without connectedAccount...")
        minimal_create_data = {
            "name": "Minimal Chatbot",
            "type": "AI"
        }
        
        minimal_create = ChatbotCreate(**minimal_create_data)
        print(f"    ✓ Minimal ChatbotCreate created: {minimal_create.name}")
        print(f"    ✓ connectedAccount is None: {minimal_create.connectedAccount is None}")
        
        print("✓ Pydantic models test passed")
        return True
        
    except Exception as e:
        print(f"✗ Pydantic models test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_service_layer():
    """Test that the service layer handles the new structure correctly"""
    print("\nTesting service layer...")
    
    try:
        from app.services.chatbot_service import ChatbotService
        from app.models import ChatbotCreate, ConnectedAccount
        
        # Create test data
        connected_account = ConnectedAccount(
            displayName="Service Test Account",
            entityType="CUSTOMER", 
            accountId=98765
        )
        
        chatbot_data = ChatbotCreate(
            name="Service Test Chatbot",
            type="AI",
            description="Testing service layer",
            welcomeMessage="Service test welcome",
            thankYouMessage="Service test goodbye",
            connectedAccount=connected_account
        )
        
        # Test service instantiation
        service = ChatbotService()
        print("  ✓ ChatbotService instantiated")
        
        # Note: We can't easily test the actual database operations without a test database
        # But we can test that the service methods exist and accept the right parameters
        
        # Check if create_chatbot method exists and has correct signature
        import inspect
        create_method = getattr(service, 'create_chatbot', None)
        if create_method:
            sig = inspect.signature(create_method)
            params = list(sig.parameters.keys())
            if 'chatbot_data' in params and 'tenant_id' in params:
                print("  ✓ create_chatbot method has correct signature")
            else:
                print(f"  ✗ create_chatbot method signature incorrect: {params}")
                return False
        else:
            print("  ✗ create_chatbot method not found")
            return False
        
        # Check update_chatbot method
        update_method = getattr(service, 'update_chatbot', None)
        if update_method:
            sig = inspect.signature(update_method)
            params = list(sig.parameters.keys())
            if 'chatbot_id' in params and 'chatbot_data' in params and 'tenant_id' in params:
                print("  ✓ update_chatbot method has correct signature")
            else:
                print(f"  ✗ update_chatbot method signature incorrect: {params}")
                return False
        else:
            print("  ✗ update_chatbot method not found")
            return False
        
        print("✓ Service layer test passed")
        return True
        
    except Exception as e:
        print(f"✗ Service layer test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_json_serialization():
    """Test that the new structure can be properly serialized/deserialized"""
    print("\nTesting JSON serialization...")
    
    try:
        from app.models import ChatbotCreate, ConnectedAccount
        
        # Create test data
        test_json = {
            "name": "JSON Test Chatbot",
            "type": "AI",
            "description": "Testing JSON serialization",
            "welcomeMessage": "JSON test welcome",
            "thankYouMessage": "JSON test goodbye",
            "connectedAccount": {
                "displayName": "JSON Test Account",
                "entityType": "LEAD",
                "accountId": 54321
            }
        }
        
        # Test deserialization from JSON
        chatbot_create = ChatbotCreate(**test_json)
        print("  ✓ Deserialization from JSON successful")
        
        # Test serialization to dict
        chatbot_dict = chatbot_create.model_dump()
        print("  ✓ Serialization to dict successful")
        
        # Verify structure
        assert 'connectedAccount' in chatbot_dict
        assert 'displayName' in chatbot_dict['connectedAccount']
        assert chatbot_dict['connectedAccount']['accountId'] == 54321
        print("  ✓ Serialized structure is correct")
        
        # Test JSON string serialization
        json_str = json.dumps(chatbot_dict)
        print("  ✓ JSON string serialization successful")
        
        # Test round-trip
        parsed_json = json.loads(json_str)
        round_trip_chatbot = ChatbotCreate(**parsed_json)
        assert round_trip_chatbot.connectedAccount.accountId == 54321
        print("  ✓ Round-trip serialization successful")
        
        print("✓ JSON serialization test passed")
        return True
        
    except Exception as e:
        print(f"✗ JSON serialization test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests"""
    print("Starting tests for new connected account structure...\n")
    
    tests = [
        ("Database Model", test_database_model),
        ("Pydantic Models", test_pydantic_models),
        ("Service Layer", test_service_layer),
        ("JSON Serialization", test_json_serialization)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} test crashed: {str(e)}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name:20} {status}")
        if result:
            passed += 1
    
    print("-"*60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The new connected account structure is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
